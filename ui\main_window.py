"""
主窗口

应用程序的主界面，整合各个组件
"""

import ttkbootstrap as ttk
from business import FormulaCalculator
from ui.components import InstructionTab, SingleTaskTab, MultiTaskTab, SettingsTab
from ui.utils.styles import StyleManager
from utils.constants import APP_NAME, APP_TITLE


class CalculatorMainWindow:
    """计算器主窗口"""

    def __init__(self):
        # 创建计算核心实例
        self.formula_calculator = FormulaCalculator()

        # 设置主题和颜色模式
        self.window = ttk.Window(themename="cosmo")
        self.window.title(APP_NAME)
        self.window.geometry("1600x1000")
        self.window.state('zoomed')

        # 设置样式
        StyleManager.setup_all_styles()

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        """创建主界面组件"""
        # 创建主容器
        self.main_container = ttk.Frame(self.window)
        self.main_container.pack(fill="both", expand=True)

        # 配置主容器的网格权重
        self.main_container.grid_rowconfigure(1, weight=1)
        self.main_container.grid_columnconfigure(0, weight=1)

        # 创建顶部标题栏
        self.create_header()

        # 创建主要内容区域
        self.create_main_content()

    def create_header(self):
        """创建标题栏"""
        style = ttk.Style()
        style.configure(
            'Header.TFrame',
            relief='flat',         
            borderwidth=0         
        )

        header_frame = ttk.Frame(self.main_container, style='Header.TFrame')
        header_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        header_frame.grid_columnconfigure(0, weight=1)

        # 标题区域
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky="", padx=30, pady=20)

        # 主标题
        main_title = ttk.Label(
            title_frame,
            text=APP_TITLE,
            font=('Microsoft YaHei', 20, 'bold'),
            foreground="#2B313F"
        )
        main_title.pack()

    def create_main_content(self):
        """创建主要内容区域"""
        # 内容容器
        content_container = ttk.Frame(self.main_container)
        content_container.grid(row=1, column=0, sticky="nsew", padx=60, pady=(0, 40))
        content_container.grid_rowconfigure(1, weight=1)  # 内容区域占主要空间
        content_container.grid_columnconfigure(0, weight=1)

        # 创建自定义选项卡头部
        self.create_custom_tab_header(content_container)
        
        # 创建内容显示区域
        self.content_frame = ttk.Frame(content_container)
        self.content_frame.grid(row=1, column=0, sticky="nsew")
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)
        
        # 创建各个选项卡内容
        self.create_all_tab_contents()
        
        # 默认显示第一个选项卡
        self.show_tab_content(0)

    def create_custom_tab_header(self, parent):
        """创建自定义选项卡头部"""
        # 配置现代化的选项卡头部样式
        style = ttk.Style()
        
        # 头部容器样式
        style.configure(
            'TabHeader.TFrame',
            background='#f8f9fa',
            borderwidth=0,
            relief='flat'
        )
        
        # 选项卡头部容器
        tab_header = ttk.Frame(parent, style='TabHeader.TFrame')
        tab_header.grid(row=0, column=0, sticky="ew")
        
        # 配置列权重，实现均匀分布
        for i in range(4):
            tab_header.grid_columnconfigure(i, weight=1)
        
        # 创建选项卡按钮
        tab_names = ["说明", "单任务", "多任务", "设置"]
        self.tab_buttons = []
        
        for i, name in enumerate(tab_names):
            btn = ttk.Button(
                tab_header,
                text=name,
                style='Modern.TabButton.TButton',
                command=lambda idx=i: self.show_tab_content(idx)
            )
            btn.grid(row=0, column=i, sticky="ew", padx=1)
            self.tab_buttons.append(btn)
        
        # 设置默认选中状态
        self.current_tab = 0
        self.update_tab_button_styles()
        
        # 设置选项卡动态宽度调整
        self.setup_dynamic_tab_width(tab_header)

    def update_tab_button_styles(self):
        """更新选项卡按钮样式"""
        style = ttk.Style()
        
        for i, btn in enumerate(self.tab_buttons):
            if i == self.current_tab:
                # 选中状态
                style.configure(f'Selected{i}.TButton', 
                              padding=[60, 12],
                              font=('Microsoft YaHei', 12, 'bold'),
                              background='#ffffff',
                              foreground='#002EA6',
                              relief='flat',
                              borderwidth=0,
                              focuscolor='none')
                # 应用选中状态的交互效果
                style.map(f'Selected{i}.TButton',
                         background=[('active', '#dee2e6')],
                         foreground=[('active', '#002EA6')])
                btn.configure(style=f'Selected{i}.TButton')
            else:
                # 未选中状态
                style.configure(f'Normal{i}.TButton',
                              padding=[60, 12],
                              font=('Microsoft YaHei', 12, 'bold'),
                              background='#e9ecef',
                              foreground='#495057',
                              relief='flat',
                              borderwidth=0,
                              focuscolor='none')
                # 应用未选中状态的交互效果
                style.map(f'Normal{i}.TButton',
                         background=[('active', '#dee2e6')],
                         foreground=[('active', '#495057')])
                btn.configure(style=f'Normal{i}.TButton')

    def show_tab_content(self, tab_index):
        """显示指定选项卡内容"""
        # 隐藏所有选项卡内容
        for widget in self.content_frame.winfo_children():
            widget.grid_remove()
        
        # 显示指定选项卡内容
        self.tab_contents[tab_index].get_frame().grid(row=0, column=0, sticky="nsew")
        
        # 更新当前选项卡索引和按钮样式
        self.current_tab = tab_index
        self.update_tab_button_styles()

    def create_all_tab_contents(self):
        """创建所有选项卡内容"""
        self.tab_contents = []
        
        # 说明选项卡
        instruction_tab = InstructionTab(self.content_frame)
        self.tab_contents.append(instruction_tab)
        
        # 单任务选项卡
        single_task_tab = SingleTaskTab(self.content_frame, self.formula_calculator)
        self.tab_contents.append(single_task_tab)
        
        # 多任务选项卡
        multi_task_tab = MultiTaskTab(self.content_frame, self.formula_calculator)
        self.tab_contents.append(multi_task_tab)
        
        # 设置选项卡
        settings_tab = SettingsTab(self.content_frame, self.formula_calculator)
        self.tab_contents.append(settings_tab)

    def setup_dynamic_tab_width(self, tab_header):
        """设置选项卡动态宽度调整"""
        def configure_tab_width():
            """配置选项卡样式以实现居中均匀分布"""
            try:
                # 获取选项卡的数量
                tab_count = len(self.tab_buttons)
                
                # 获取实际容器宽度
                self.window.update_idletasks()
                container_width = tab_header.winfo_width()
                
                if container_width <= 1:  # 如果还没有正确获取到宽度，使用默认值
                    container_width = 1540  # 1600 - 60 (边距)
                
                # 计算每个选项卡的宽度（基于总宽度均匀分布）
                tab_width = container_width // tab_count
                
                # 计算合适的 padding
                text_widths = {
                    "说明": 2 * 14,      # 2个中文字符
                    "单任务": 3 * 14,    # 3个中文字符  
                    "多任务": 3 * 14,    # 3个中文字符
                    "设置": 2 * 14       # 2个中文字符
                }
                max_text_width = max(text_widths.values())
                
                # 计算padding：(目标宽度 - 文本宽度) / 2，但保持在合理范围内
                padding_x = max(20, (tab_width - max_text_width) // 4)
                padding_x = min(padding_x, 80)  # 限制最大padding
                
                # 更新选项卡按钮样式
                style = ttk.Style()
                
                # 更新基础样式
                style.configure(
                    'Modern.TabButton.TButton',
                    padding=[padding_x, 12],
                    font=('Microsoft YaHei', 12, 'bold'),
                    background='#e9ecef',
                    foreground='#495057',
                    relief='flat',
                    borderwidth=0,
                    focuscolor='none'
                )
                
                # 更新所有按钮的样式以反映新的padding
                self.update_tab_button_styles()
                
            except Exception as e:
                print(f"配置选项卡样式时出错: {e}")
        
        # 立即更新一次
        self.window.after(100, configure_tab_width)
        
        # 绑定窗口大小变化事件
        self.window.bind('<Configure>', 
                        lambda event: self.window.after(50, configure_tab_width) 
                        if event.widget == self.window else None)

    def run(self):
        """运行应用程序"""
        self.window.mainloop()
