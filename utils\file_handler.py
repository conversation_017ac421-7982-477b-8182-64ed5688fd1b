"""
文件处理工具

提供文件操作相关的工具函数
"""

import os
import shutil
import pandas as pd
from typing import Optional, List, Dict
from .constants import DEFAULT_MATERIALS_FILE, TEMPLATE_FILE


class FileHandler:
    """文件处理工具类"""

    @staticmethod
    def get_project_root() -> str:
        """获取项目根目录"""
        # utils/file_handler.py -> utils -> project_root
        current_dir = os.path.dirname(os.path.abspath(__file__))  # utils目录
        return os.path.dirname(current_dir)  # 项目根目录

    @staticmethod
    def get_file_path(filename: str) -> str:
        """获取项目文件的完整路径"""
        return os.path.join(FileHandler.get_project_root(), filename)

    @staticmethod
    def file_exists(filename: str) -> bool:
        """检查文件是否存在"""
        file_path = FileHandler.get_file_path(filename)
        return os.path.exists(file_path)

    @staticmethod
    def copy_file(source_filename: str, target_path: str) -> bool:
        """复制文件"""
        try:
            source_path = FileHandler.get_file_path(source_filename)
            if not os.path.exists(source_path):
                print(f"源文件不存在: {source_path}")
                return False
            
            shutil.copy2(source_path, target_path)
            return True
        except Exception as e:
            print(f"复制文件失败: {e}")
            return False

    @staticmethod
    def read_excel(filename: str) -> Optional[pd.DataFrame]:
        """读取Excel文件"""
        try:
            file_path = FileHandler.get_file_path(filename)
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return None
            
            return pd.read_excel(file_path)
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None

    @staticmethod
    def write_excel(data: pd.DataFrame, file_path: str, sheet_name: str = 'Sheet1') -> bool:
        """写入Excel文件"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                data.to_excel(writer, sheet_name=sheet_name, index=False)
            return True
        except Exception as e:
            print(f"写入Excel文件失败: {e}")
            return False

    @staticmethod
    def write_excel_multiple_sheets(data_dict: Dict[str, pd.DataFrame], file_path: str) -> bool:
        """写入多个工作表的Excel文件"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name, data in data_dict.items():
                    data.to_excel(writer, sheet_name=sheet_name, index=False)
            return True
        except Exception as e:
            print(f"写入Excel文件失败: {e}")
            return False

    @staticmethod
    def ensure_directory_exists(directory_path: str) -> bool:
        """确保目录存在"""
        try:
            os.makedirs(directory_path, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False

    @staticmethod
    def get_default_materials_file_path() -> str:
        """获取默认材料文件路径"""
        return FileHandler.get_file_path(DEFAULT_MATERIALS_FILE)

    @staticmethod
    def get_template_file_path() -> str:
        """获取模板文件路径"""
        return FileHandler.get_file_path(TEMPLATE_FILE)

    @staticmethod
    def validate_excel_columns(df: pd.DataFrame, required_columns: List[str]) -> bool:
        """验证Excel文件是否包含必需的列"""
        return all(col in df.columns for col in required_columns)

    @staticmethod
    def clean_excel_data(df: pd.DataFrame) -> pd.DataFrame:
        """清理Excel数据"""
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 移除完全空白的列
        df = df.dropna(axis=1, how='all')
        
        # 填充NaN值为空字符串
        df = df.fillna('')
        
        return df

    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"

    @staticmethod
    def get_file_info(filename: str) -> Optional[Dict]:
        """获取文件信息"""
        try:
            file_path = FileHandler.get_file_path(filename)
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                'path': file_path,
                'size': stat.st_size,
                'size_formatted': FileHandler.format_file_size(stat.st_size),
                'modified_time': stat.st_mtime,
                'exists': True
            }
        except Exception as e:
            print(f"获取文件信息失败: {e}")
            return None
