"""
数据库操作层

负责与SQLite数据库的直接交互，提供基础的CRUD操作
"""

import sqlite3
import os
from typing import Dict, Optional, List
import pandas as pd


class MaterialsDatabase:
    """材料数据库操作类"""

    def __init__(self, db_file: str = None):
        """初始化SQLite数据库连接"""
        if db_file is None:
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_file = os.path.join(current_dir, 'materials.db')

        self.db_file = db_file
        self.is_memory_db = db_file == ":memory:"
        self._memory_conn = None

        # 对于内存数据库，保持连接打开
        if self.is_memory_db:
            self._memory_conn = sqlite3.connect(self.db_file)

        self.init_database()

    def _get_connection(self):
        """获取数据库连接"""
        if self.is_memory_db:
            return self._memory_conn
        else:
            return sqlite3.connect(self.db_file)

    def _execute_with_connection(self, operation):
        """使用连接执行操作"""
        if self.is_memory_db:
            return operation(self._memory_conn)
        else:
            with sqlite3.connect(self.db_file) as conn:
                return operation(conn)

    def init_database(self) -> bool:
        """初始化数据库表结构"""
        try:
            if self.is_memory_db:
                conn = self._memory_conn
                cursor = conn.cursor()
            else:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()

            # 创建原料表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS materials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    atomic_number INTEGER,
                    element TEXT NOT NULL,
                    formula TEXT,
                    ratio REAL,
                    excess REAL,
                    molecular_weight REAL,
                    cas TEXT,
                    company TEXT,
                    product_no TEXT,
                    purity REAL,
                    pricing REAL,
                    is_selected INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
            print(f"数据库表初始化成功: {self.db_file}")
            return True

        except Exception as e:
            print(f"初始化数据库失败: {e}")
            return False

    def get_all_materials(self) -> pd.DataFrame:
        """获取所有原料数据"""
        try:
            def operation(conn):
                return pd.read_sql_query("SELECT * FROM materials ORDER BY id", conn)

            return self._execute_with_connection(operation)

        except Exception as e:
            print(f"获取原料数据时出错: {e}")
            return pd.DataFrame()

    def get_material_by_element(self, element: str) -> Optional[pd.Series]:
        """根据元素获取原料信息（返回选中的，如果没有选中的则返回第一个）"""
        try:
            def operation(conn):
                # 首先尝试获取选中的材料
                df = pd.read_sql_query(
                    "SELECT * FROM materials WHERE element = ? AND is_selected = 1 ORDER BY id LIMIT 1",
                    conn, params=(element,)
                )
                if not df.empty:
                    return df.iloc[0]

                # 如果没有选中的，返回第一个
                df = pd.read_sql_query(
                    "SELECT * FROM materials WHERE element = ? ORDER BY id LIMIT 1",
                    conn, params=(element,)
                )
                if not df.empty:
                    return df.iloc[0]
                return None

            return self._execute_with_connection(operation)

        except Exception as e:
            print(f"查询元素 {element} 时出错: {e}")
            return None

    def get_selected_materials(self) -> pd.DataFrame:
        """获取所有选中的材料"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                df = pd.read_sql_query("SELECT * FROM materials WHERE is_selected = 1 ORDER BY id", conn)
                return df

        except Exception as e:
            print(f"获取选中材料时出错: {e}")
            return pd.DataFrame()

    def add_material(self, material_data: Dict) -> bool:
        """添加新的原料数据"""
        try:
            def operation(conn):
                cursor = conn.cursor()

                # 准备插入数据
                columns = []
                values = []
                placeholders = []

                # 映射UI列名到数据库列名
                column_mapping = {
                    'Z': 'atomic_number',
                    'Element': 'element',
                    'Formula': 'formula',
                    'Ratio': 'ratio',
                    'Excess': 'excess',
                    'Mr': 'molecular_weight',
                    'CAS': 'cas',
                    'Co.': 'company',
                    'Product No.': 'product_no',
                    'Purity': 'purity',
                    'Pricing': 'pricing'
                }

                for ui_key, value in material_data.items():
                    db_column = column_mapping.get(ui_key, ui_key.lower())
                    if db_column in ['atomic_number', 'element', 'formula', 'ratio', 'excess',
                                   'molecular_weight', 'cas', 'company', 'product_no', 'purity', 'pricing']:
                        columns.append(db_column)
                        # 对于必需字段，如果为空则提供默认值
                        if db_column == 'element' and (value == '' or value is None):
                            values.append('Unknown')  # 为空白行提供默认元素名
                        else:
                            values.append(value if value != '' else None)
                        placeholders.append('?')

                # 添加默认的选择状态
                if 'is_selected' not in [col.replace('_', '') for col in columns]:
                    columns.append('is_selected')
                    values.append(material_data.get('is_selected', 0))
                    placeholders.append('?')

                if not columns:
                    print("没有有效的插入字段")
                    return False

                sql = f"INSERT INTO materials ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(sql, values)
                conn.commit()

                if cursor.rowcount > 0:
                    print(f"成功添加新原料，ID: {cursor.lastrowid}")
                    return True
                else:
                    print("添加原料失败")
                    return False

            return self._execute_with_connection(operation)

        except Exception as e:
            print(f"添加原料数据时出错: {e}")
            return False

    def update_material(self, material_id: int, material_data: Dict) -> bool:
        """更新原料数据"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()

                # 准备更新数据
                set_clauses = []
                values = []

                for key, value in material_data.items():
                    if key.lower() in ['no', 'z', 'element', 'formula', 'ratio', 'excess',
                                     'mr', 'cas', 'company', 'product_no', 'purity', 'pricing', 'is_selected']:
                        column_name = key.lower().replace('.', '').replace(' ', '_')
                        set_clauses.append(f"{column_name} = ?")
                        values.append(value)

                if not set_clauses:
                    print("没有有效的更新字段")
                    return False

                # 添加更新时间
                set_clauses.append("updated_at = CURRENT_TIMESTAMP")
                values.append(material_id)

                sql = f"UPDATE materials SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, values)
                conn.commit()

                if cursor.rowcount > 0:
                    print(f"成功更新原料 ID: {material_id}")
                    return True
                else:
                    print(f"未找到 ID 为 {material_id} 的原料")
                    return False

        except Exception as e:
            print(f"更新原料数据时出错: {e}")
            return False

    def delete_material(self, material_id: int) -> bool:
        """删除原料数据"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM materials WHERE id = ?", (material_id,))
                conn.commit()

                if cursor.rowcount > 0:
                    print(f"成功删除原料 ID: {material_id}")
                    return True
                else:
                    print(f"未找到 ID 为 {material_id} 的原料")
                    return False

        except Exception as e:
            print(f"删除原料数据时出错: {e}")
            return False

    def toggle_material_selection(self, material_id: int) -> bool:
        """切换材料的选择状态，确保每个element只有一个被选中"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()

                # 获取当前材料信息
                cursor.execute("SELECT element, is_selected FROM materials WHERE id = ?", (material_id,))
                result = cursor.fetchone()
                if not result:
                    print(f"未找到 ID 为 {material_id} 的材料")
                    return False

                element, current_selected = result

                if current_selected:
                    # 如果当前是选中状态，取消选中
                    cursor.execute("UPDATE materials SET is_selected = 0 WHERE id = ?", (material_id,))
                else:
                    # 如果当前未选中，先取消同element的其他选中项，然后选中当前项
                    cursor.execute("UPDATE materials SET is_selected = 0 WHERE element = ?", (element,))
                    cursor.execute("UPDATE materials SET is_selected = 1 WHERE id = ?", (material_id,))

                conn.commit()
                print(f"成功切换材料 ID: {material_id} 的选择状态")
                return True

        except Exception as e:
            print(f"切换材料选择状态时出错: {e}")
            return False

    def toggle_material_selection_direct(self, material_id: int, selected: bool) -> bool:
        """直接设置材料的选择状态"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()

                # 直接设置选择状态
                cursor.execute("UPDATE materials SET is_selected = ? WHERE id = ?", (1 if selected else 0, material_id))
                conn.commit()
                return True

        except Exception as e:
            print(f"设置材料选择状态时出错: {e}")
            return False

    def clear_all_data(self) -> bool:
        """清空所有数据"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM materials")
                conn.commit()
                print("已清空所有数据")
                return True

        except Exception as e:
            print(f"清空数据时出错: {e}")
            return False

    def import_from_excel(self, file_path: str, replace_all: bool = True) -> bool:
        """从Excel文件导入数据，每个元素默认选中纯度最低的材料"""
        try:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return False

            # 读取Excel文件
            df = pd.read_excel(file_path)

            if replace_all:
                # 清空现有数据
                self.clear_all_data()

            # 首先导入所有数据，不设置选择状态
            imported_count = 0
            for _, row in df.iterrows():
                material_data = row.to_dict()
                material_data['is_selected'] = 0  # 先都设为未选中

                if self.add_material(material_data):
                    imported_count += 1

            # 然后为每个元素选择纯度最低的材料
            self._select_lowest_purity_materials()

            print(f"成功导入 {imported_count} 条数据，已为每个元素选择纯度最低的材料")
            return imported_count > 0

        except Exception as e:
            print(f"导入数据时出错: {e}")
            return False

    def _select_lowest_purity_materials(self):
        """为每个元素选择纯度最低的材料"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()

                # 获取所有不同的元素
                cursor.execute("SELECT DISTINCT element FROM materials WHERE element IS NOT NULL AND element != ''")
                elements = [row[0] for row in cursor.fetchall()]

                for element in elements:
                    # 获取该元素的所有材料，按纯度升序排列（纯度最低的在前）
                    cursor.execute("""
                        SELECT id, purity FROM materials
                        WHERE element = ? AND purity IS NOT NULL AND purity != ''
                        ORDER BY CAST(purity AS REAL) ASC
                    """, (element,))

                    materials = cursor.fetchall()
                    if materials:
                        # 选择纯度最低的材料（第一个）
                        lowest_purity_id = materials[0][0]

                        # 先取消该元素所有材料的选择
                        cursor.execute("UPDATE materials SET is_selected = 0 WHERE element = ?", (element,))

                        # 选中纯度最低的材料
                        cursor.execute("UPDATE materials SET is_selected = 1 WHERE id = ?", (lowest_purity_id,))

                        print(f"元素 {element}: 选择了纯度最低的材料 (ID: {lowest_purity_id}, 纯度: {materials[0][1]})")

        except Exception as e:
            print(f"选择最低纯度材料时出错: {e}")

    def get_column_names(self) -> List[str]:
        """获取数据库表的列名"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(materials)")
                columns_info = cursor.fetchall()

                # 提取列名，排除id和时间戳字段
                column_names = []
                for col_info in columns_info:
                    col_name = col_info[1]  # 列名在索引1
                    if col_name not in ['id', 'created_at', 'updated_at']:
                        column_names.append(col_name)

                return column_names

        except Exception as e:
            print(f"获取列名时出错: {e}")
            return []
