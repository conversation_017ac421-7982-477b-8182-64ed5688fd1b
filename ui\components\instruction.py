"""
说明页面组件

显示应用程序使用说明
"""

import ttkbootstrap as ttk
from ui.utils.styles import StyleManager


class InstructionTab:
    """说明选项卡组件"""

    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        self.create_widgets()

    def create_widgets(self):
        """创建说明页面组件"""
        # 配置网格权重
        self.frame.grid_rowconfigure(0, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.frame)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=30)
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 确保样式已配置
        StyleManager.setup_modern_labelframe_style()

        # 创建带框的说明区域
        instruction_frame = ttk.LabelFrame(
            main_frame,
            text="使用说明",
            style='Modern.TLabelframe',
            padding=25
        )
        instruction_frame.grid(row=0, column=0, sticky="nsew")
        instruction_frame.grid_rowconfigure(0, weight=1)
        instruction_frame.grid_columnconfigure(0, weight=1)

        # 说明内容
        instruction_text = """🔹 单任务计算：
   输入单个化学式和目标质量，计算所需原料配比

🔹 多任务计算：
   批量导入多个化学式，一次性计算所有配比

🔹 设置：
   管理原料数据库，支持从Excel文件导入原料信息

🔹 使用步骤：
   1. 选择计算模式（单任务或多任务）
   2. 输入化学式和目标质量
   3. 点击计算按钮
   4. 查看结果并导出Excel报告

🔹 注意事项：
   • 化学式格式：支持标准化学式格式，如 BaTiO3、Al2O3等
   • 括号支持：支持复杂化学式，如 Ca(OH)2、Mg3(PO4)2等
   • 系数支持：支持化学式前的系数，如 2NaCl、0.5CaCO3等
   • 原料选择：每种元素可以有多个原料选项，系统使用选中的原料进行计算

🔹 数据管理：
   • 初始化：从默认Excel文件加载原料数据，自动为每个元素选择纯度最低的材料
   • 导入：支持从自定义Excel文件导入原料数据
   • 编辑：双击表格单元格可直接编辑数据
   • 选择：双击Pick列可切换原料的选择状态，每个元素只能选择一种原料"""

        ttk.Label(
            instruction_frame,
            text=instruction_text,
            font=('Microsoft YaHei', 12),
            foreground='#495057',
            justify='left'
        ).grid(row=0, column=0, sticky="nw")

    def get_frame(self):
        """获取组件框架"""
        return self.frame
