#!/usr/bin/env python3
"""
陶瓷配方计算器 - 主入口文件

这是应用程序的主入口点，负责启动整个应用程序。

使用方法:
    python main.py

作者: niziqi
版本: 1.0
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from ui.main_window import CalculatorMainWindow
    from utils.constants import APP_TITLE
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖包已正确安装")
    sys.exit(1)


def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'ttkbootstrap',
        'pandas',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下必要的依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_data_files():
    """检查必要的数据文件"""
    required_files = [
        'oxide_powders_initial.xlsx',
        'oxide_powders_template.xlsx'
    ]
    
    missing_files = []
    
    for filename in required_files:
        file_path = os.path.join(project_root, filename)
        if not os.path.exists(file_path):
            missing_files.append(filename)
    
    if missing_files:
        print("缺少以下必要的数据文件:")
        for filename in missing_files:
            print(f"  - {filename}")
        print("\n请确保这些文件存在于项目根目录中")
        return False
    
    return True


def main():
    """主函数"""
    print(f"正在启动 {APP_TITLE}...")
    
    # 检查依赖包
    if not check_dependencies():
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查数据文件
    if not check_data_files():
        print("警告: 缺少数据文件，程序可能无法正常工作")
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    try:
        # 创建并运行主窗口
        app = CalculatorMainWindow()
        print("应用程序启动成功!")
        app.run()
        
    except Exception as e:
        print(f"应用程序运行时出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
