"""
对话框工具

提供常用的对话框功能
"""

from tkinter import messagebox, filedialog
from datetime import datetime
from typing import Optional


class DialogUtils:
    """对话框工具类"""

    @staticmethod
    def show_error(title: str, message: str):
        """显示错误对话框"""
        messagebox.showerror(title, message)

    @staticmethod
    def show_info(title: str, message: str):
        """显示信息对话框"""
        messagebox.showinfo(title, message)

    @staticmethod
    def show_warning(title: str, message: str):
        """显示警告对话框"""
        messagebox.showwarning(title, message)

    @staticmethod
    def ask_yes_no(title: str, message: str) -> bool:
        """显示是否确认对话框"""
        return messagebox.askyesno(title, message)

    @staticmethod
    def get_save_file_path(file_prefix: str, file_extension: str = ".xlsx") -> Optional[str]:
        """获取保存文件路径"""
        return filedialog.asksaveasfilename(
            defaultextension=file_extension,
            filetypes=[("Excel 文件", "*.xlsx")],
            initialfile=f"{file_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_extension}"
        )

    @staticmethod
    def get_open_file_path(title: str = "选择文件", file_types: list = None) -> Optional[str]:
        """获取打开文件路径"""
        if file_types is None:
            file_types = [("Excel文件", "*.xlsx"), ("Excel文件", "*.xls")]
        
        return filedialog.askopenfilename(
            title=title,
            filetypes=file_types,
            initialdir="."
        )




