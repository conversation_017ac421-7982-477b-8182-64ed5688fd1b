"""
数据管理层

提供高级数据管理功能，包括数据转换、事件通知等
"""

import pandas as pd
from typing import Dict, List, Callable, Optional, Any
from data.database import MaterialsDatabase
from utils.file_handler import FileHandler


class MaterialsDataManager:
    """材料数据管理器，统一管理材料数据的增删改查"""

    def __init__(self, db_file: str = None):
        self.database = MaterialsDatabase(db_file)
        self._change_listeners: List[Callable] = []
        self.load_materials_from_database()

    def add_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """添加数据变更监听器"""
        self._change_listeners.append(listener)

    def remove_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """移除数据变更监听器"""
        if listener in self._change_listeners:
            self._change_listeners.remove(listener)

    def _notify_change(self, change_type: str, data: Any = None) -> None:
        """通知数据变更"""
        for listener in self._change_listeners:
            try:
                listener(change_type, data)
            except Exception as e:
                print(f"通知监听器时出错: {e}")

    def get_materials_data(self) -> List[Dict]:
        """获取原料数据，从数据库读取并转换格式"""
        try:
            df = self.database.get_all_materials()
            materials_data = df.to_dict('records')

            # 转换格式以适配UI
            for item in materials_data:
                self._convert_database_fields_to_ui_format(item)

            return materials_data
        except Exception as e:
            print(f"获取原料数据失败: {e}")
            return []

    def _convert_database_fields_to_ui_format(self, item: Dict) -> None:
        """将数据库字段转换为UI期望的格式"""
        # 将is_selected转换为_selected以保持兼容性
        item['_selected'] = bool(item.get('is_selected', 0))

        # 字段映射表
        field_mapping = {
            'atomic_number': 'Z',
            'company': 'Co.',
            'product_no': 'Product No.',
            'element': 'Element',
            'formula': 'Formula',
            'ratio': 'Ratio',
            'excess': 'Excess',
            'molecular_weight': 'Mr',
            'cas': 'CAS',
            'purity': 'Purity',
            'pricing': 'Pricing'
        }

        # 转换字段名
        for db_field, ui_field in field_mapping.items():
            if db_field in item:
                item[ui_field] = item[db_field]

        # 规范化显示：Z 应为整数显示
        try:
            z_val = item.get('atomic_number', None)
            if z_val is None or (isinstance(z_val, float) and pd.isna(z_val)):
                item['Z'] = ''
            else:
                # 允许字符串/浮点，统一转为整数
                item['Z'] = int(float(z_val))
        except Exception:
            # 出现异常时，保持为空字符串以避免显示 3.0
            item['Z'] = ''

    def load_materials_from_database(self) -> bool:
        """从数据库加载原料数据"""
        try:
            # 检查数据库是否有数据
            df = self.database.get_all_materials()
            if df.empty:
                # 如果数据库为空，从Excel初始化
                print("数据库为空，从Excel文件初始化...")
                default_file = FileHandler.get_default_materials_file_path()
                if self.database.import_from_excel(default_file):
                    self._notify_change('materials_loaded')
                    return True
                else:
                    print("从Excel初始化失败")
                    return False
            else:
                self._notify_change('materials_loaded')
                return True
        except Exception as e:
            print(f"加载原料数据失败: {e}")
            return False

    def load_materials_from_file(self, file_path: str = None) -> bool:
        """从Excel文件重新初始化数据库"""
        try:
            # 如果没有指定文件路径，使用默认文件
            if file_path is None:
                file_path = FileHandler.get_default_materials_file_path()
            # 如果指定的是文件名而不是完整路径，尝试从项目根目录获取
            elif not file_path.startswith('/') and not file_path.startswith('\\') and ':' not in file_path:
                file_path = FileHandler.get_file_path(file_path)

            success = self.database.import_from_excel(file_path, replace_all=True)
            if success:
                self._notify_change('materials_loaded')
            return success
        except Exception as e:
            print(f"从文件加载原料数据失败: {e}")
            return False

    def toggle_material_selection(self, row_index: int) -> bool:
        """切换材料选择状态"""
        try:
            materials_data = self.get_materials_data()
            if 0 <= row_index < len(materials_data):
                material = materials_data[row_index]
                material_id = material.get('id')
                if material_id:
                    success = self.database.toggle_material_selection(material_id)
                    if success:
                        self._notify_change('selection_changed')
                    return success
            return False
        except Exception as e:
            print(f"切换材料选择状态失败: {e}")
            return False

    def reset_to_defaults(self) -> bool:
        """重置到默认数据"""
        return self.load_materials_from_file('oxide_powders_initial.xlsx')

    def update_material_data(self, row_index: int, column_name: str, new_value: Any) -> bool:
        """更新材料数据"""
        try:
            materials_data = self.get_materials_data()
            if 0 <= row_index < len(materials_data):
                material = materials_data[row_index]
                material_id = material.get('id')
                if material_id:
                    # 转换列名以匹配数据库字段
                    db_column_name = self._convert_ui_field_to_database_field(column_name)
                    success = self.database.update_material(material_id, {db_column_name: new_value})
                    if success:
                        self._notify_change('data_updated')
                    return success
            return False
        except Exception as e:
            print(f"更新材料数据失败: {e}")
            return False

    def _convert_ui_field_to_database_field(self, ui_field: str) -> str:
        """将UI字段名转换为数据库字段名"""
        field_mapping = {
            'Z': 'atomic_number',
            'Co.': 'company',
            'Product No.': 'product_no',
            'Element': 'element',
            'Formula': 'formula',
            'Ratio': 'ratio',
            'Excess': 'excess',
            'Mr': 'molecular_weight',
            'CAS': 'cas',
            'Purity': 'purity',
            'Pricing': 'pricing'
        }
        return field_mapping.get(ui_field, ui_field.lower())

    def delete_material(self, row_index: int) -> bool:
        """删除材料"""
        try:
            materials_data = self.get_materials_data()
            if 0 <= row_index < len(materials_data):
                material = materials_data[row_index]
                material_id = material.get('id')
                if material_id:
                    success = self.database.delete_material(material_id)
                    if success:
                        self._notify_change('material_deleted')
                    return success
            return False
        except Exception as e:
            print(f"删除材料失败: {e}")
            return False

    def add_material(self, material_data: Dict) -> bool:
        """添加材料"""
        try:
            # 转换列名以匹配数据库字段
            db_material_data = self._convert_ui_data_to_database_format(material_data)

            # 新添加的材料默认不选中
            db_material_data['is_selected'] = 0

            success = self.database.add_material(db_material_data)
            if success:
                # 添加成功后，重新为该元素选择纯度最低的材料
                element = db_material_data.get('Element')
                if element:
                    self._select_lowest_purity_for_element(element)
                self._notify_change('material_added')
            return success
        except Exception as e:
            print(f"添加材料失败: {e}")
            return False

    def _select_lowest_purity_for_element(self, element: str):
        """为指定元素选择纯度最低的材料"""
        try:
            # 获取该元素的所有材料
            materials_data = self.get_materials_data()
            element_materials = [m for m in materials_data if m.get('Element') == element]

            if not element_materials:
                return

            # 找到纯度最低的材料
            lowest_purity_material = None
            lowest_purity = float('inf')

            for material in element_materials:
                purity_str = material.get('Purity', '')
                if purity_str and purity_str != '':
                    try:
                        purity = float(purity_str)
                        if purity < lowest_purity:
                            lowest_purity = purity
                            lowest_purity_material = material
                    except (ValueError, TypeError):
                        continue

            # 如果找到了纯度最低的材料，选择它
            if lowest_purity_material:
                material_id = lowest_purity_material.get('id')
                if material_id:
                    # 先取消该元素所有材料的选择
                    for material in element_materials:
                        if material.get('id'):
                            self.database.toggle_material_selection_direct(material.get('id'), False)

                    # 选中纯度最低的材料
                    self.database.toggle_material_selection_direct(material_id, True)
                    print(f"元素 {element}: 自动选择了纯度最低的材料 (纯度: {lowest_purity})")

        except Exception as e:
            print(f"为元素 {element} 选择最低纯度材料时出错: {e}")

    def _convert_ui_data_to_database_format(self, ui_data: Dict) -> Dict:
        """将UI数据格式转换为数据库格式"""
        db_data = {}
        for key, value in ui_data.items():
            db_field = self._convert_ui_field_to_database_field(key)
            db_data[db_field] = value
        return db_data

    def import_from_excel(self, file_path: str) -> bool:
        """从Excel导入数据，导入前清空数据库，每个元素默认选中纯度最低的材料"""
        try:
            # 导入前清空数据库，并为每个元素选择纯度最低的材料
            success = self.database.import_from_excel(file_path, replace_all=True)
            if success:
                self._notify_change('data_updated')
            return success
        except Exception as e:
            print(f"导入Excel失败: {e}")
            return False

    def get_material_by_element(self, element: str) -> Optional[Dict]:
        """根据元素获取原料信息"""
        try:
            material_series = self.database.get_material_by_element(element)
            if material_series is not None:
                material = material_series.to_dict()
                self._convert_database_fields_to_ui_format(material)
                return material
            return None
        except Exception as e:
            print(f"获取元素材料失败: {e}")
            return None

    def get_selected_materials(self) -> List[Dict]:
        """获取所有选中的材料"""
        try:
            df = self.database.get_selected_materials()
            materials_data = df.to_dict('records')

            # 转换格式以适配UI
            for item in materials_data:
                self._convert_database_fields_to_ui_format(item)

            return materials_data
        except Exception as e:
            print(f"获取选中材料失败: {e}")
            return []

    def clear_all_data(self) -> bool:
        """清空所有数据"""
        try:
            success = self.database.clear_all_data()
            if success:
                self._notify_change('data_cleared')
            return success
        except Exception as e:
            print(f"清空数据失败: {e}")
            return False
